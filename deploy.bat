@echo off
echo ========================================
echo DataDrivenRenamer 部署脚本
echo ========================================

REM 设置Qt环境
set QT_DIR=D:\Qt\6.9.1\mingw_64
set QT_TOOLS_DIR=D:\Qt\Tools\mingw1310_64
set PATH=%QT_DIR%\bin;%QT_TOOLS_DIR%\bin;%PATH%

REM 检查Qt工具是否可用
where windeployqt.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到windeployqt.exe工具
    echo 请确保Qt安装正确并且路径设置正确
    pause
    exit /b 1
)

REM 构建Release版本
echo 正在构建Release版本...
call build_with_qt.bat
if %errorlevel% neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

REM 检查可执行文件是否存在
if not exist "release\DataDrivenRenamer.exe" (
    echo 错误: 找不到可执行文件 release\DataDrivenRenamer.exe
    pause
    exit /b 1
)

REM 创建部署目录
echo 创建部署目录...
if exist "deploy" rmdir /s /q "deploy"
mkdir "deploy"

REM 复制可执行文件
echo 复制可执行文件...
copy "release\DataDrivenRenamer.exe" "deploy\"

REM 使用windeployqt部署Qt依赖
echo 部署Qt依赖库...
cd deploy
windeployqt.exe --release --no-translations --no-system-d3d-compiler --no-opengl-sw DataDrivenRenamer.exe
if %errorlevel% neq 0 (
    echo 警告: windeployqt执行时出现问题，但可能仍然成功
)
cd ..

REM 复制MinGW运行时库（如果需要）
echo 检查并复制MinGW运行时库...
if exist "%QT_TOOLS_DIR%\bin\libgcc_s_seh-1.dll" (
    copy "%QT_TOOLS_DIR%\bin\libgcc_s_seh-1.dll" "deploy\" >nul 2>&1
)
if exist "%QT_TOOLS_DIR%\bin\libstdc++-6.dll" (
    copy "%QT_TOOLS_DIR%\bin\libstdc++-6.dll" "deploy\" >nul 2>&1
)
if exist "%QT_TOOLS_DIR%\bin\libwinpthread-1.dll" (
    copy "%QT_TOOLS_DIR%\bin\libwinpthread-1.dll" "deploy\" >nul 2>&1
)

REM 创建README文件
echo 创建说明文件...
echo DataDrivenRenamer - 数据驱动批量重命名工具 > "deploy\README.txt"
echo. >> "deploy\README.txt"
echo 使用方法: >> "deploy\README.txt"
echo 1. 双击 DataDrivenRenamer.exe 启动程序 >> "deploy\README.txt"
echo 2. 添加要重命名的源文件 >> "deploy\README.txt"
echo 3. 在右侧粘贴或输入新的文件名列表 >> "deploy\README.txt"
echo 4. 点击"解析数据"预览重命名结果 >> "deploy\README.txt"
echo 5. 确认无误后点击"执行重命名" >> "deploy\README.txt"
echo. >> "deploy\README.txt"
echo 注意: 程序会自动为新文件名添加原文件的扩展名 >> "deploy\README.txt"

REM 测试部署的程序
echo 测试部署的程序...
cd deploy
echo 正在启动程序进行测试...
start "" "DataDrivenRenamer.exe"
cd ..

echo ========================================
echo 部署完成！
echo ========================================
echo 部署文件位置: deploy\
echo 主程序: deploy\DataDrivenRenamer.exe
echo.
echo 您可以将整个 deploy 文件夹打包分享给其他人
echo 或者创建安装包进行分发
echo.
pause
