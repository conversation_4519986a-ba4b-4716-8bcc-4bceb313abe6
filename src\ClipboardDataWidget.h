#ifndef CLIPBOARDDATAWIDGET_H
#define CLIPBOARDDATAWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QPushButton>
#include <QLabel>
#include <QListWidget>
#include <QSplitter>
#include <QClipboard>
#include <QApplication>
#include <QStringList>

class ClipboardDataWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ClipboardDataWidget(QWidget *parent = nullptr);
    
    // Public interface
    int getNameCount() const;
    QStringList getNewNames() const;
    void clear();

signals:
    void dataChanged();

private slots:
    void onPasteFromClipboard();
    void onParseData();
    void onClearData();
    void onTextChanged();

private:
    void setupUI();
    void parseClipboardData(const QString &data);
    QStringList extractNamesFromText(const QString &text);
    void updateNameCount();
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QSplitter *m_splitter;
    
    // Input section
    QWidget *m_inputWidget;
    QVBoxLayout *m_inputLayout;
    QHBoxLayout *m_inputButtonLayout;
    QTextEdit *m_inputTextEdit;
    QPushButton *m_pasteButton;
    QPushButton *m_parseButton;
    QPushButton *m_clearButton;
    QLabel *m_inputLabel;
    
    // Preview section
    QWidget *m_previewWidget;
    QVBoxLayout *m_previewLayout;
    QListWidget *m_namesList;
    QLabel *m_countLabel;
    
    // Data
    QStringList m_newNames;
};

#endif // CLIPBOARDDATAWIDGET_H
