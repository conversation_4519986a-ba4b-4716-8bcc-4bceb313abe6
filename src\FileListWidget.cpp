#include "FileListWidget.h"
#include <QFileInfo>
#include <QUrl>
#include <QMessageBox>

FileListWidget::FileListWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_buttonLayout(nullptr)
    , m_fileList(nullptr)
    , m_addButton(nullptr)
    , m_removeButton(nullptr)
    , m_clearButton(nullptr)
    , m_countLabel(nullptr)
{
    setupUI();
    setAcceptDrops(true);
}

void FileListWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    
    // File list
    m_fileList = new QListWidget(this);
    m_fileList->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_fileList->setDragDropMode(QAbstractItemView::DropOnly);
    m_mainLayout->addWidget(m_fileList);
    
    // Buttons
    m_buttonLayout = new QHBoxLayout();
    m_addButton = new QPushButton("添加文件", this);
    m_removeButton = new QPushButton("移除选中", this);
    m_clearButton = new QPushButton("清空", this);
    
    m_addButton->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    m_removeButton->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    m_clearButton->setIcon(style()->standardIcon(QStyle::SP_DialogResetButton));
    
    m_buttonLayout->addWidget(m_addButton);
    m_buttonLayout->addWidget(m_removeButton);
    m_buttonLayout->addWidget(m_clearButton);
    m_buttonLayout->addStretch();
    
    m_mainLayout->addLayout(m_buttonLayout);
    
    // Count label
    m_countLabel = new QLabel("文件数量: 0", this);
    m_countLabel->setStyleSheet("QLabel { color: #888; font-size: 12px; }");
    m_mainLayout->addWidget(m_countLabel);
    
    // Connect signals
    connect(m_addButton, &QPushButton::clicked, this, &FileListWidget::onAddFiles);
    connect(m_removeButton, &QPushButton::clicked, this, &FileListWidget::onRemoveSelected);
    connect(m_clearButton, &QPushButton::clicked, this, &FileListWidget::onClearAll);
}

int FileListWidget::getFileCount() const
{
    return m_filePaths.count();
}

QStringList FileListWidget::getFilePaths() const
{
    return m_filePaths;
}

QStringList FileListWidget::getFileNames() const
{
    QStringList names;
    for (const QString &path : m_filePaths) {
        QFileInfo info(path);
        names.append(info.fileName());
    }
    return names;
}

void FileListWidget::clear()
{
    m_filePaths.clear();
    m_fileList->clear();
    updateFileCount();
    emit filesChanged();
}

void FileListWidget::onAddFiles()
{
    QStringList files = QFileDialog::getOpenFileNames(
        this,
        "选择要重命名的文件",
        QString(),
        "所有文件 (*.*)"
    );
    
    if (!files.isEmpty()) {
        addFiles(files);
    }
}

void FileListWidget::onRemoveSelected()
{
    QList<QListWidgetItem*> selectedItems = m_fileList->selectedItems();
    if (selectedItems.isEmpty()) {
        return;
    }
    
    // Remove from back to front to maintain indices
    for (int i = selectedItems.count() - 1; i >= 0; --i) {
        int row = m_fileList->row(selectedItems[i]);
        m_filePaths.removeAt(row);
        delete m_fileList->takeItem(row);
    }
    
    updateFileCount();
    emit filesChanged();
}

void FileListWidget::onClearAll()
{
    clear();
}

void FileListWidget::addFiles(const QStringList &filePaths)
{
    int addedCount = 0;
    
    for (const QString &path : filePaths) {
        QFileInfo info(path);
        if (info.exists() && info.isFile() && !m_filePaths.contains(path)) {
            m_filePaths.append(path);
            
            QListWidgetItem *item = new QListWidgetItem(info.fileName());
            item->setToolTip(path);
            item->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
            m_fileList->addItem(item);
            
            addedCount++;
        }
    }
    
    if (addedCount > 0) {
        updateFileCount();
        emit filesChanged();
    }
}

void FileListWidget::updateFileCount()
{
    m_countLabel->setText(QString("文件数量: %1").arg(m_filePaths.count()));
}

void FileListWidget::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    }
}

void FileListWidget::dropEvent(QDropEvent *event)
{
    QStringList filePaths;
    
    for (const QUrl &url : event->mimeData()->urls()) {
        if (url.isLocalFile()) {
            QString path = url.toLocalFile();
            QFileInfo info(path);
            if (info.isFile()) {
                filePaths.append(path);
            }
        }
    }
    
    if (!filePaths.isEmpty()) {
        addFiles(filePaths);
        event->acceptProposedAction();
    }
}
