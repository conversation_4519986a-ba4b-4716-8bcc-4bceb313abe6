{"version": "2.0.0", "tasks": [{"label": "Build with qmake", "type": "shell", "command": "qmake", "args": ["DataDrivenRenamer.pro"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"env": {"PATH": "D:/Qt/6.9.1/mingw_64/bin;D:/Qt/Tools/mingw1310_64/bin;${env:PATH}"}}, "problemMatcher": []}, {"label": "Build with mingw32-make", "type": "shell", "command": "mingw32-make", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"env": {"PATH": "D:/Qt/6.9.1/mingw_64/bin;D:/Qt/Tools/mingw1310_64/bin;${env:PATH}"}}, "dependsOn": "Build with qmake", "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Clean", "type": "shell", "command": "rm", "args": ["-rf", "release", "debug", "Makefile*"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Full Build", "dependsOrder": "sequence", "dependsOn": ["Clean", "Build with mingw32-make"], "group": "build"}]}