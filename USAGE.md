# 使用指南

## 快速开始

### 1. 启动应用程序
运行 `DataDrivenRenamer.exe` 启动应用程序。

### 2. 界面介绍
应用程序采用三栏布局：

#### 左栏：源文件 (Source Files)
- 显示待重命名的文件列表
- 支持拖拽文件到此区域
- 点击"添加文件"按钮选择文件
- 可以选择多个文件进行批量操作

#### 中栏：新文件名来源 (New Filenames Source)  
- 上半部分：文本输入区域
- 下半部分：解析结果预览
- 支持从剪贴板粘贴数据
- 自动解析多种数据格式

#### 右栏：预览与执行 (Preview & Execute)
- 显示原文件名→新文件名的映射关系
- 显示操作状态（就绪/错误/警告）
- 执行重命名操作

## 详细使用步骤

### 步骤1: 添加源文件
有两种方式添加文件：

**方式A: 拖拽添加**
1. 从文件管理器中选择要重命名的文件
2. 直接拖拽到左侧"源文件"面板中

**方式B: 按钮添加**
1. 点击左侧面板的"添加文件"按钮
2. 在文件对话框中选择要重命名的文件
3. 支持多选（按住Ctrl键选择多个文件）

### 步骤2: 准备新文件名数据
这是本软件的核心特色功能！

**从Excel复制粘贴：**
1. 在Excel中选择包含新文件名的列
2. 复制（Ctrl+C）
3. 在中栏点击"从剪贴板粘贴"按钮
4. 或直接在文本框中粘贴（Ctrl+V）

**从网页复制粘贴：**
1. 在网页中选择文件名列表
2. 复制选中的内容
3. 粘贴到中栏的文本框中

**手动输入：**
1. 在中栏文本框中直接输入新文件名
2. 每行一个文件名

**支持的数据格式：**
- 每行一个文件名
- 制表符分隔的数据（提取第一列）
- 逗号分隔的数据
- 带编号的列表（如：1. 文件名.txt）

### 步骤3: 预览映射关系
1. 添加源文件和新文件名后，右侧会自动显示映射预览
2. 检查映射关系是否正确
3. 注意查看状态列：
   - ✓ 就绪：可以安全重命名
   - ✗ 错误：存在问题，需要修正
   - ⚠ 警告：需要注意的情况

### 步骤4: 执行重命名
1. 确认预览无误后，点击"执行重命名"按钮
2. 系统会弹出确认对话框
3. 确认后开始重命名操作
4. 进度条显示操作进度
5. 完成后显示结果统计

## 实际使用场景

### 场景1: 从Excel表格重命名文件
假设您有一个Excel表格，包含新的文件名列表：

1. 在Excel中选择文件名列（例如A列）
2. 复制整列数据
3. 在DataDrivenRenamer中粘贴
4. 系统自动解析并显示预览
5. 执行重命名

### 场景2: 从网页内容重命名
假设您从网页复制了一个文件列表：

1. 选择网页中的文件名列表
2. 复制到剪贴板
3. 在软件中粘贴
4. 系统智能识别并提取文件名
5. 预览并执行

### 场景3: 批量添加前缀或后缀
虽然本软件主要用于数据映射，但也可以用于批量添加前缀：

1. 准备带前缀的新文件名列表
2. 例如：
   ```
   [重要]文档1.txt
   [重要]文档2.txt
   [重要]文档3.txt
   ```
3. 按正常流程操作

## 注意事项

### 安全性
- 重命名操作会创建备份文件
- 如果操作失败，系统会自动回滚
- 建议在重要文件操作前手动备份

### 文件名限制
- 不能包含以下字符：`< > : " / \ | ? *`
- 不能使用Windows保留名称（如CON、PRN等）
- 系统会自动清理无效字符

### 数量匹配
- 源文件数量和新文件名数量最好相等
- 如果数量不匹配，系统会按最小数量进行映射
- 多余的项目会在预览中显示警告

## 故障排除

### 问题1: 粘贴的数据格式混乱
**解决方案**: 
- 检查原始数据格式
- 尝试逐行手动输入几个测试
- 使用"解析数据"按钮重新解析

### 问题2: 文件名包含无效字符
**解决方案**:
- 系统会自动替换无效字符为下划线
- 在预览中检查替换结果
- 如需要，手动修改文本框中的内容

### 问题3: 目标文件已存在
**解决方案**:
- 预览中会显示冲突警告
- 修改新文件名以避免冲突
- 或先移动/重命名现有文件

## 高级技巧

### 技巧1: 使用Excel公式生成文件名
在Excel中使用公式生成复杂的文件名模式：
```excel
=CONCATENATE("项目_", A1, "_", TEXT(TODAY(), "YYYYMMDD"), ".txt")
```

### 技巧2: 批量处理不同类型文件
1. 先处理一种类型的文件（如.txt）
2. 清空列表
3. 再处理另一种类型的文件（如.jpg）

### 技巧3: 保存常用的文件名模板
将常用的文件名列表保存在文本文件中，需要时复制粘贴。

## 反馈和支持

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看本使用指南
2. 检查INSTALL.md中的故障排除部分
3. 提交Issue到项目仓库
