{"version": "0.2.0", "configurations": [{"name": "Debug DataDrivenRenamer", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/release/DataDrivenRenamer.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:/Qt/6.9.1/mingw_64/bin;D:/Qt/Tools/mingw1310_64/bin;${env:PATH}"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "D:/Qt/Tools/mingw1310_64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Build with mingw32-make"}]}