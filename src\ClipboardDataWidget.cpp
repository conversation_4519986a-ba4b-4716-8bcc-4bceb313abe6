#include "ClipboardDataWidget.h"
#include <QRegularExpression>
#include <QMessageBox>

ClipboardDataWidget::ClipboardDataWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_splitter(nullptr)
    , m_inputWidget(nullptr)
    , m_inputLayout(nullptr)
    , m_inputButtonLayout(nullptr)
    , m_inputTextEdit(nullptr)
    , m_pasteButton(nullptr)
    , m_parseButton(nullptr)
    , m_clearButton(nullptr)
    , m_inputLabel(nullptr)
    , m_previewWidget(nullptr)
    , m_previewLayout(nullptr)
    , m_namesList(nullptr)
    , m_countLabel(nullptr)
{
    setupUI();
}

void ClipboardDataWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_splitter = new QSplitter(Qt::Vertical, this);
    m_mainLayout->addWidget(m_splitter);
    
    // Input section
    m_inputWidget = new QWidget();
    m_inputLayout = new QVBoxLayout(m_inputWidget);
    
    m_inputLabel = new QLabel("从剪贴板粘贴数据 (支持Excel、网页等格式):");
    m_inputLabel->setStyleSheet("QLabel { font-weight: bold; color: #333; }");
    m_inputLayout->addWidget(m_inputLabel);
    
    m_inputTextEdit = new QTextEdit();
    m_inputTextEdit->setPlaceholderText(
        "在此粘贴从Excel、网页或其他来源复制的文件名列表...\n\n"
        "支持的格式:\n"
        "• 每行一个文件名\n"
        "• 制表符分隔的数据 (会提取第一列)\n"
        "• 逗号分隔的数据\n"
        "• 带编号的列表 (如: 1. 文件名.txt)\n"
        "\n"
        "示例:\n"
        "新文件1.txt\n"
        "新文件2.jpg\n"
        "新文件3.pdf"
    );
    m_inputTextEdit->setMaximumHeight(150);
    m_inputLayout->addWidget(m_inputTextEdit);
    
    // Input buttons
    m_inputButtonLayout = new QHBoxLayout();
    m_pasteButton = new QPushButton("从剪贴板粘贴");
    m_parseButton = new QPushButton("解析数据");
    m_clearButton = new QPushButton("清空");
    
    m_pasteButton->setIcon(style()->standardIcon(QStyle::SP_DialogApplyButton));
    m_parseButton->setIcon(style()->standardIcon(QStyle::SP_ComputerIcon));
    m_clearButton->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    
    m_inputButtonLayout->addWidget(m_pasteButton);
    m_inputButtonLayout->addWidget(m_parseButton);
    m_inputButtonLayout->addWidget(m_clearButton);
    m_inputButtonLayout->addStretch();
    
    m_inputLayout->addLayout(m_inputButtonLayout);
    m_splitter->addWidget(m_inputWidget);
    
    // Preview section
    m_previewWidget = new QWidget();
    m_previewLayout = new QVBoxLayout(m_previewWidget);
    
    QLabel *previewLabel = new QLabel("解析结果预览:");
    previewLabel->setStyleSheet("QLabel { font-weight: bold; color: #333; }");
    m_previewLayout->addWidget(previewLabel);
    
    m_namesList = new QListWidget();
    m_namesList->setAlternatingRowColors(true);
    m_previewLayout->addWidget(m_namesList);
    
    m_countLabel = new QLabel("文件名数量: 0");
    m_countLabel->setStyleSheet("QLabel { color: #666; font-size: 12px; }");
    m_previewLayout->addWidget(m_countLabel);
    
    m_splitter->addWidget(m_previewWidget);
    
    // Set splitter proportions
    m_splitter->setStretchFactor(0, 1);
    m_splitter->setStretchFactor(1, 1);
    
    // Connect signals
    connect(m_pasteButton, &QPushButton::clicked, this, &ClipboardDataWidget::onPasteFromClipboard);
    connect(m_parseButton, &QPushButton::clicked, this, &ClipboardDataWidget::onParseData);
    connect(m_clearButton, &QPushButton::clicked, this, &ClipboardDataWidget::onClearData);
    connect(m_inputTextEdit, &QTextEdit::textChanged, this, &ClipboardDataWidget::onTextChanged);
}

int ClipboardDataWidget::getNameCount() const
{
    return m_newNames.count();
}

QStringList ClipboardDataWidget::getNewNames() const
{
    return m_newNames;
}

void ClipboardDataWidget::clear()
{
    m_inputTextEdit->clear();
    m_namesList->clear();
    m_newNames.clear();
    updateNameCount();
    emit dataChanged();
}

void ClipboardDataWidget::onPasteFromClipboard()
{
    QClipboard *clipboard = QApplication::clipboard();
    QString text = clipboard->text();
    
    if (text.isEmpty()) {
        QMessageBox::information(this, "提示", "剪贴板中没有文本数据");
        return;
    }
    
    m_inputTextEdit->setPlainText(text);
    onParseData();
}

void ClipboardDataWidget::onParseData()
{
    QString text = m_inputTextEdit->toPlainText().trimmed();
    if (text.isEmpty()) {
        return;
    }
    
    parseClipboardData(text);
}

void ClipboardDataWidget::onClearData()
{
    clear();
}

void ClipboardDataWidget::onTextChanged()
{
    // Auto-parse if text is not too long
    QString text = m_inputTextEdit->toPlainText().trimmed();
    if (!text.isEmpty() && text.length() < 10000) {
        parseClipboardData(text);
    }
}

void ClipboardDataWidget::parseClipboardData(const QString &data)
{
    QStringList names = extractNamesFromText(data);
    
    m_newNames = names;
    m_namesList->clear();
    
    for (int i = 0; i < names.count(); ++i) {
        QListWidgetItem *item = new QListWidgetItem(QString("%1. %2").arg(i + 1).arg(names[i]));
        item->setToolTip(names[i]);
        m_namesList->addItem(item);
    }
    
    updateNameCount();
    emit dataChanged();
}

QStringList ClipboardDataWidget::extractNamesFromText(const QString &text)
{
    QStringList result;
    QStringList lines = text.split('\n', Qt::SkipEmptyParts);
    
    for (QString line : lines) {
        line = line.trimmed();
        if (line.isEmpty()) continue;
        
        // Remove common prefixes like numbers, bullets, etc.
        QRegularExpression prefixRegex(R"(^[\d\s\.\)\-\*\+]*\s*)");
        line = line.remove(prefixRegex);
        
        // Handle tab-separated or comma-separated data (take first column)
        if (line.contains('\t')) {
            QStringList parts = line.split('\t', Qt::SkipEmptyParts);
            if (!parts.isEmpty()) {
                line = parts.first().trimmed();
            }
        } else if (line.contains(',') && !line.contains('.')) {
            // Only split on comma if it doesn't look like a filename with extension
            QStringList parts = line.split(',', Qt::SkipEmptyParts);
            if (!parts.isEmpty()) {
                line = parts.first().trimmed();
            }
        }
        
        // Remove quotes
        line = line.remove('"').remove('\'');
        
        if (!line.isEmpty()) {
            result.append(line);
        }
    }
    
    return result;
}

void ClipboardDataWidget::updateNameCount()
{
    m_countLabel->setText(QString("文件名数量: %1").arg(m_newNames.count()));
}
