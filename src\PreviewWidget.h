#ifndef PREVIEWWIDGET_H
#define PREVIEWWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QLabel>
#include <QPushButton>
#include <QHeaderView>
#include <QStringList>

class PreviewWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PreviewWidget(QWidget *parent = nullptr);
    
    // Public interface
    void updatePreview(const QStringList &sourceFiles, const QStringList &newNames);
    void clear();
    int getMappingCount() const;
    
    // Get mapping data for rename operation
    struct RenameMapping {
        QString sourcePath;
        QString sourceFileName;
        QString newFileName;
        QString newPath;
        bool isValid;
        QString errorMessage;
    };
    
    QList<RenameMapping> getMappings() const;

signals:
    void previewUpdated();

private slots:
    void onRefreshPreview();

private:
    void setupUI();
    void updateMappingCount();
    RenameMapping createMapping(const QString &sourcePath, const QString &newName, int index);
    bool validateNewFileName(const QString &fileName);
    QString sanitizeFileName(const QString &fileName);
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_headerLayout;
    QTreeWidget *m_previewTree;
    QLabel *m_countLabel;
    QPushButton *m_refreshButton;
    
    // Data
    QList<RenameMapping> m_mappings;
    QStringList m_sourceFiles;
    QStringList m_newNames;
};

#endif // PREVIEWWIDGET_H
