#include "MappingAlgorithm.h"
#include <QFileInfo>
#include <QRegularExpression>

MappingAlgorithm::MappingAlgorithm(QObject *parent)
    : QObject(parent)
{
}

MappingAlgorithm::MappingResult MappingAlgorithm::createMapping(
    const QStringList &sourceFiles, 
    const QStringList &targetNames,
    MappingMode mode)
{
    MappingResult result;
    result.isValid = false;
    result.mappedCount = 0;
    result.skippedSource = 0;
    result.skippedTarget = 0;
    
    // Validate inputs
    if (!validateLists(sourceFiles, targetNames, result.warnings)) {
        return result;
    }
    
    // Sanitize target names
    QStringList sanitizedNames = sanitizeFileNames(targetNames);
    
    int sourceCount = sourceFiles.count();
    int targetCount = sanitizedNames.count();
    
    switch (mode) {
        case OneToOne:
            if (sourceCount == targetCount) {
                result.sourceFiles = sourceFiles;
                result.targetNames = sanitizedNames;
                result.mappedCount = sourceCount;
            } else {
                result.warnings.append(QString("数量不匹配: %1 个源文件, %2 个目标名称").arg(sourceCount).arg(targetCount));
                // Fall back to truncate mode
                int minCount = qMin(sourceCount, targetCount);
                result.sourceFiles = sourceFiles.mid(0, minCount);
                result.targetNames = sanitizedNames.mid(0, minCount);
                result.mappedCount = minCount;
                result.skippedSource = sourceCount - minCount;
                result.skippedTarget = targetCount - minCount;
            }
            break;
            
        case TruncateSource:
            {
                int minCount = qMin(sourceCount, targetCount);
                result.sourceFiles = sourceFiles.mid(0, minCount);
                result.targetNames = sanitizedNames.mid(0, minCount);
                result.mappedCount = minCount;
                result.skippedSource = sourceCount - minCount;
            }
            break;
            
        case TruncateTarget:
            {
                int minCount = qMin(sourceCount, targetCount);
                result.sourceFiles = sourceFiles.mid(0, minCount);
                result.targetNames = sanitizedNames.mid(0, minCount);
                result.mappedCount = minCount;
                result.skippedTarget = targetCount - minCount;
            }
            break;
            
        case PadTarget:
            result.sourceFiles = sourceFiles;
            result.targetNames = sanitizedNames;
            // Pad target names if needed
            for (int i = targetCount; i < sourceCount; ++i) {
                QFileInfo info(sourceFiles[i]);
                result.targetNames.append(generateDefaultName(info.fileName(), i));
            }
            result.mappedCount = sourceCount;
            break;
            
        case PadSource:
            {
                int minCount = qMin(sourceCount, targetCount);
                result.sourceFiles = sourceFiles.mid(0, minCount);
                result.targetNames = sanitizedNames.mid(0, minCount);
                result.mappedCount = minCount;
                result.skippedTarget = targetCount - minCount;
            }
            break;
    }
    
    result.isValid = result.mappedCount > 0;
    return result;
}

bool MappingAlgorithm::validateLists(const QStringList &sourceFiles, const QStringList &targetNames, QStringList &errors)
{
    bool isValid = true;
    
    if (sourceFiles.isEmpty()) {
        errors.append("源文件列表为空");
        isValid = false;
    }
    
    if (targetNames.isEmpty()) {
        errors.append("目标名称列表为空");
        isValid = false;
    }
    
    // Check for duplicate source files
    QStringList uniqueSources = sourceFiles;
    uniqueSources.removeDuplicates();
    if (uniqueSources.count() != sourceFiles.count()) {
        errors.append("源文件列表包含重复项");
    }
    
    // Check if source files exist
    for (const QString &file : sourceFiles) {
        if (!QFileInfo::exists(file)) {
            errors.append(QString("源文件不存在: %1").arg(file));
            isValid = false;
        }
    }
    
    return isValid;
}

QStringList MappingAlgorithm::sanitizeFileNames(const QStringList &names)
{
    QStringList result;
    for (const QString &name : names) {
        result.append(sanitizeFileName(name));
    }
    return result;
}

QString MappingAlgorithm::sanitizeFileName(const QString &name)
{
    QString result = name.trimmed();
    
    // Replace invalid characters
    QRegularExpression invalidChars(R"([<>:"/\\|?*])");
    result = result.replace(invalidChars, "_");
    
    // Remove leading/trailing dots and spaces
    result = result.remove(QRegularExpression(R"(^[\.\s]+|[\.\s]+$)"));
    
    // Ensure it's not empty
    if (result.isEmpty()) {
        result = "unnamed_file";
    }
    
    return result;
}

bool MappingAlgorithm::isValidFileName(const QString &name)
{
    if (name.isEmpty()) {
        return false;
    }
    
    // Check for invalid characters
    QRegularExpression invalidChars(R"([<>:"/\\|?*])");
    if (name.contains(invalidChars)) {
        return false;
    }
    
    // Check for reserved names on Windows
    QStringList reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
    
    QString baseName = QFileInfo(name).baseName().toUpper();
    return !reservedNames.contains(baseName);
}

int MappingAlgorithm::findOptimalMapping(const QStringList &sourceFiles, const QStringList &targetNames)
{
    // Simple heuristic: return the minimum count
    return qMin(sourceFiles.count(), targetNames.count());
}

QStringList MappingAlgorithm::suggestMappingMode(const QStringList &sourceFiles, const QStringList &targetNames)
{
    QStringList suggestions;
    
    int sourceCount = sourceFiles.count();
    int targetCount = targetNames.count();
    
    if (sourceCount == targetCount) {
        suggestions.append("一对一映射 (推荐)");
    } else if (sourceCount > targetCount) {
        suggestions.append("截断源文件列表");
        suggestions.append("用默认名称填充目标列表");
    } else {
        suggestions.append("截断目标名称列表");
        suggestions.append("跳过多余的目标名称");
    }
    
    return suggestions;
}

QString MappingAlgorithm::generateDefaultName(const QString &originalName, int index)
{
    QFileInfo info(originalName);
    QString baseName = info.baseName();
    QString extension = info.suffix();
    
    if (extension.isEmpty()) {
        return QString("%1_renamed_%2").arg(baseName).arg(index + 1);
    } else {
        return QString("%1_renamed_%2.%3").arg(baseName).arg(index + 1).arg(extension);
    }
}

QStringList MappingAlgorithm::removeDuplicates(const QStringList &names)
{
    QStringList result = names;
    result.removeDuplicates();
    return result;
}
