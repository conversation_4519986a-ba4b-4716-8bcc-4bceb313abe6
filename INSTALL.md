# 安装和构建指南

## 前置要求

### 1. Qt6 开发环境
下载并安装Qt6开发环境：
- 访问 [Qt官网](https://www.qt.io/download)
- 下载Qt Online Installer
- 安装时选择以下组件：
  - Qt 6.5.x (或更新版本)
  - MinGW 11.2.0 64-bit (Windows)
  - Qt Creator IDE (可选，但推荐)

### 2. 构建工具
根据您选择的构建方式，需要以下工具之一：

#### 选项A: CMake (推荐)
- 下载并安装 [CMake](https://cmake.org/download/)
- 确保CMake添加到系统PATH中

#### 选项B: qmake (Qt自带)
- 随Qt安装自动提供
- 无需额外安装

## 构建方法

### 方法1: 使用CMake
```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -G "MinGW Makefiles"

# 构建
cmake --build .

# 运行
cd bin
./DataDrivenRenamer.exe
```

### 方法2: 使用qmake
```bash
# 创建构建目录
mkdir build
cd build

# 生成Makefile
qmake ../DataDrivenRenamer.pro

# 构建
mingw32-make

# 运行
cd bin
./DataDrivenRenamer.exe
```

### 方法3: 使用Qt Creator (最简单)
1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择 `DataDrivenRenamer.pro` 文件
4. 配置构建套件 (Kit)
5. 点击构建按钮 (Ctrl+B)
6. 点击运行按钮 (Ctrl+R)

## 自动构建脚本

### Windows
运行 `build.bat` 脚本：
```cmd
build.bat
```

### Linux/macOS
运行 `build.sh` 脚本：
```bash
chmod +x build.sh
./build.sh
```

## 故障排除

### 问题1: "qmake: command not found"
**解决方案**: 
- 确保Qt已正确安装
- 将Qt的bin目录添加到系统PATH中
- 例如: `C:\Qt\6.5.0\mingw_64\bin`

### 问题2: "cmake: command not found"
**解决方案**:
- 下载并安装CMake
- 确保CMake添加到系统PATH中

### 问题3: 编译错误
**解决方案**:
- 确保使用C++17兼容的编译器
- 检查Qt版本是否为6.0或更高
- 确保所有依赖项都已正确安装

### 问题4: 运行时缺少DLL
**解决方案**:
- 确保Qt的bin目录在系统PATH中
- 或者将必要的Qt DLL复制到可执行文件目录

## 开发环境设置

### Visual Studio Code
如果使用VS Code开发，推荐安装以下扩展：
- C/C++ Extension Pack
- Qt tools
- CMake Tools

### 环境变量设置
添加以下环境变量到系统PATH：
```
C:\Qt\6.5.0\mingw_64\bin
C:\Qt\Tools\mingw1120_64\bin
C:\Qt\Tools\CMake_64\bin
```

## 项目结构说明

```
DataDrivenRenamer/
├── src/                    # 源代码目录
│   ├── main.cpp           # 程序入口
│   ├── MainWindow.*       # 主窗口
│   ├── FileListWidget.*   # 文件列表组件
│   ├── ClipboardDataWidget.* # 剪贴板数据组件
│   ├── PreviewWidget.*    # 预览组件
│   ├── RenameEngine.*     # 重命名引擎
│   └── MappingAlgorithm.* # 映射算法
├── CMakeLists.txt         # CMake配置文件
├── DataDrivenRenamer.pro  # qmake项目文件
├── build.bat              # Windows构建脚本
├── build.sh               # Linux/macOS构建脚本
├── INSTALL.md             # 安装指南
└── README.md              # 项目说明
```

## 下一步

构建成功后，您可以：
1. 运行程序测试基本功能
2. 查看README.md了解使用方法
3. 根据需要修改源代码
4. 提交Issue或Pull Request改进项目
