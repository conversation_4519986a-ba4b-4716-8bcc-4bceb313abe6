@echo off
echo Creating test files for DataDrivenRenamer...

REM Create test directory if it doesn't exist
if not exist test_files mkdir test_files
cd test_files

REM Create some sample files for testing
echo This is test file 1 > file1.txt
echo This is test file 2 > file2.txt
echo This is test file 3 > file3.txt
echo This is test document 1 > document1.doc
echo This is test document 2 > document2.doc
echo This is test image 1 > image1.jpg
echo This is test image 2 > image2.jpg
echo This is test data 1 > data1.csv
echo This is test data 2 > data2.csv
echo This is test presentation > presentation.ppt

echo Test files created successfully!
echo.
echo Files created:
dir /b *.txt *.doc *.jpg *.csv *.ppt

echo.
echo You can now use these files to test the DataDrivenRenamer application.
echo.
echo Example new names to copy and paste:
echo 新文档1.txt
echo 新文档2.txt  
echo 新文档3.txt
echo 重要文档1.doc
echo 重要文档2.doc
echo 照片1.jpg
echo 照片2.jpg
echo 数据表1.csv
echo 数据表2.csv
echo 演示文稿.ppt

pause
