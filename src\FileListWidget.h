#ifndef FILELISTWIDGET_H
#define FILELISTWIDGET_H

#include <QWidget>
#include <QListWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QFileDialog>
#include <QStringList>

class FileListWidget : public QWidget
{
    Q_OBJECT

public:
    explicit FileListWidget(QWidget *parent = nullptr);
    
    // Public interface
    int getFileCount() const;
    QStringList getFilePaths() const;
    QStringList getFileNames() const;
    void clear();

signals:
    void filesChanged();

private slots:
    void onAddFiles();
    void onRemoveSelected();
    void onClearAll();

private:
    void setupUI();
    void addFiles(const QStringList &filePaths);
    void updateFileCount();
    
    // Drag and drop support
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_buttonLayout;
    QListWidget *m_fileList;
    QPushButton *m_addButton;
    QPushButton *m_removeButton;
    QPushButton *m_clearButton;
    QLabel *m_countLabel;
    
    // Data
    QStringList m_filePaths;
};

#endif // FILELISTWIDGET_H
