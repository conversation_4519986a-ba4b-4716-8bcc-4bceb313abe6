#!/bin/bash

echo "Building DataDrivenRenamer with Qt6..."

# Set Qt environment
export PATH="/d/Qt/6.9.1/mingw_64/bin:/d/Qt/Tools/mingw1120_64/bin:$PATH"

# Clean previous build
echo "Cleaning previous build..."
rm -rf release debug
rm -f Makefile Makefile.Debug Makefile.Release

# Generate Makefile
echo "Generating Makefile..."
qmake DataDrivenRenamer.pro
if [ $? -ne 0 ]; then
    echo "Error: qmake failed"
    exit 1
fi

# Build the project
echo "Building project..."
mingw32-make
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo "Build completed successfully!"
echo "Executable: release/DataDrivenRenamer.exe"
