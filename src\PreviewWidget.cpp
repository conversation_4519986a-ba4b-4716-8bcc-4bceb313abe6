#include "PreviewWidget.h"
#include <QFileInfo>
#include <QDir>
#include <QRegularExpression>

PreviewWidget::PreviewWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_headerLayout(nullptr)
    , m_previewTree(nullptr)
    , m_countLabel(nullptr)
    , m_refreshButton(nullptr)
{
    setupUI();
}

void PreviewWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    
    // Header
    m_headerLayout = new QHBoxLayout();
    m_countLabel = new QLabel("映射数量: 0");
    m_countLabel->setStyleSheet("QLabel { font-weight: bold; color: #333; }");
    
    m_refreshButton = new QPushButton("刷新预览");
    m_refreshButton->setIcon(style()->standardIcon(QStyle::SP_BrowserReload));
    
    m_headerLayout->addWidget(m_countLabel);
    m_headerLayout->addStretch();
    m_headerLayout->addWidget(m_refreshButton);
    
    m_mainLayout->addLayout(m_headerLayout);
    
    // Preview tree
    m_previewTree = new QTreeWidget();
    m_previewTree->setHeaderLabels(QStringList() << "原文件名" << "新文件名" << "状态");
    m_previewTree->setAlternatingRowColors(true);
    m_previewTree->setRootIsDecorated(false);
    
    // Set column widths
    m_previewTree->header()->setStretchLastSection(false);
    m_previewTree->header()->setSectionResizeMode(0, QHeaderView::Stretch);
    m_previewTree->header()->setSectionResizeMode(1, QHeaderView::Stretch);
    m_previewTree->header()->setSectionResizeMode(2, QHeaderView::ResizeToContents);
    
    m_mainLayout->addWidget(m_previewTree);
    
    // Connect signals
    connect(m_refreshButton, &QPushButton::clicked, this, &PreviewWidget::onRefreshPreview);
}

void PreviewWidget::updatePreview(const QStringList &sourceFiles, const QStringList &newNames)
{
    m_sourceFiles = sourceFiles;
    m_newNames = newNames;
    
    m_mappings.clear();
    m_previewTree->clear();
    
    int minCount = qMin(sourceFiles.count(), newNames.count());
    int maxCount = qMax(sourceFiles.count(), newNames.count());
    
    // Create mappings for available pairs
    for (int i = 0; i < minCount; ++i) {
        RenameMapping mapping = createMapping(sourceFiles[i], newNames[i], i);
        m_mappings.append(mapping);
        
        QTreeWidgetItem *item = new QTreeWidgetItem();
        QFileInfo sourceInfo(sourceFiles[i]);
        item->setText(0, sourceInfo.fileName());
        item->setText(1, mapping.newFileName);
        
        if (mapping.isValid) {
            item->setText(2, "✓ 就绪");
            item->setForeground(2, QColor(0, 150, 0));
        } else {
            item->setText(2, "✗ " + mapping.errorMessage);
            item->setForeground(2, QColor(200, 0, 0));
        }
        
        item->setToolTip(0, sourceFiles[i]);
        item->setToolTip(1, mapping.newPath);
        
        m_previewTree->addTopLevelItem(item);
    }
    
    // Show unmatched items
    if (sourceFiles.count() > newNames.count()) {
        for (int i = minCount; i < sourceFiles.count(); ++i) {
            QTreeWidgetItem *item = new QTreeWidgetItem();
            QFileInfo sourceInfo(sourceFiles[i]);
            item->setText(0, sourceInfo.fileName());
            item->setText(1, "");
            item->setText(2, "⚠ 缺少新名称");
            item->setForeground(2, QColor(200, 100, 0));
            item->setToolTip(0, sourceFiles[i]);
            m_previewTree->addTopLevelItem(item);
        }
    } else if (newNames.count() > sourceFiles.count()) {
        for (int i = minCount; i < newNames.count(); ++i) {
            QTreeWidgetItem *item = new QTreeWidgetItem();
            item->setText(0, "");
            item->setText(1, newNames[i]);
            item->setText(2, "⚠ 缺少源文件");
            item->setForeground(2, QColor(200, 100, 0));
            m_previewTree->addTopLevelItem(item);
        }
    }
    
    updateMappingCount();
    emit previewUpdated();
}

void PreviewWidget::clear()
{
    m_sourceFiles.clear();
    m_newNames.clear();
    m_mappings.clear();
    m_previewTree->clear();
    updateMappingCount();
}

int PreviewWidget::getMappingCount() const
{
    return m_mappings.count();
}

QList<PreviewWidget::RenameMapping> PreviewWidget::getMappings() const
{
    return m_mappings;
}

void PreviewWidget::onRefreshPreview()
{
    updatePreview(m_sourceFiles, m_newNames);
}

void PreviewWidget::updateMappingCount()
{
    int validMappings = 0;
    for (const RenameMapping &mapping : m_mappings) {
        if (mapping.isValid) {
            validMappings++;
        }
    }
    
    m_countLabel->setText(QString("映射数量: %1/%2 有效").arg(validMappings).arg(m_mappings.count()));
}

PreviewWidget::RenameMapping PreviewWidget::createMapping(const QString &sourcePath, const QString &newName, int index)
{
    RenameMapping mapping;
    mapping.sourcePath = sourcePath;
    mapping.isValid = false;
    
    QFileInfo sourceInfo(sourcePath);
    mapping.sourceFileName = sourceInfo.fileName();
    
    // Sanitize new filename and automatically append source file extension
    QString sanitizedName = sanitizeFileName(newName);

    // Get source file extension
    QString sourceExtension = sourceInfo.suffix();

    // Check if the new name already has an extension
    QFileInfo newNameInfo(sanitizedName);
    if (newNameInfo.suffix().isEmpty() && !sourceExtension.isEmpty()) {
        // Append source file extension if new name doesn't have one
        sanitizedName = sanitizedName + "." + sourceExtension;
    }

    mapping.newFileName = sanitizedName;
    
    // Validate new filename
    if (!validateNewFileName(sanitizedName)) {
        mapping.errorMessage = "无效的文件名";
        return mapping;
    }
    
    // Create new path
    QString newPath = sourceInfo.absolutePath() + "/" + sanitizedName;
    mapping.newPath = newPath;
    
    // Check if target already exists
    if (QFileInfo::exists(newPath) && newPath != sourcePath) {
        mapping.errorMessage = "目标文件已存在";
        return mapping;
    }
    
    // Check if source file still exists
    if (!sourceInfo.exists()) {
        mapping.errorMessage = "源文件不存在";
        return mapping;
    }
    
    mapping.isValid = true;
    return mapping;
}

bool PreviewWidget::validateNewFileName(const QString &fileName)
{
    if (fileName.isEmpty()) {
        return false;
    }
    
    // Check for invalid characters on Windows
    QRegularExpression invalidChars(R"([<>:"/\\|?*])");
    if (fileName.contains(invalidChars)) {
        return false;
    }
    
    // Check for reserved names on Windows
    QStringList reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
    
    QString baseName = QFileInfo(fileName).baseName().toUpper();
    if (reservedNames.contains(baseName)) {
        return false;
    }
    
    return true;
}

QString PreviewWidget::sanitizeFileName(const QString &fileName)
{
    QString result = fileName.trimmed();
    
    // Replace invalid characters
    result = result.replace(QRegularExpression(R"([<>:"/\\|?*])"), "_");
    
    // Remove leading/trailing dots and spaces
    result = result.remove(QRegularExpression(R"(^[\.\s]+|[\.\s]+$)"));
    
    // Ensure it's not empty
    if (result.isEmpty()) {
        result = "unnamed_file";
    }
    
    return result;
}
