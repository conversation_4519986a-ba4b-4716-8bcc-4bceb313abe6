# DataDrivenRenamer 构建说明

## 问题解决

您遇到的"无法打开源文件"错误已经解决！虽然系统环境变量中已经正确配置了Qt路径，但VSCode的C++扩展需要额外的配置来正确识别Qt头文件。

### 已解决的问题：
1. ✅ **VSCode C++配置**：创建了`.vscode/c_cpp_properties.json`配置文件
2. ✅ **构建任务配置**：创建了`.vscode/tasks.json`构建任务
3. ✅ **调试配置**：创建了`.vscode/launch.json`调试配置
4. ✅ **项目成功构建**：可执行文件已生成在`release/DataDrivenRenamer.exe`

## 解决方案

### 方案1：使用提供的构建脚本（推荐）

#### Windows批处理脚本
```bash
build_with_qt.bat
```

#### Bash脚本（Git Bash/WSL）
```bash
./build_with_qt.sh
```

### 方案2：手动构建

1. **设置环境变量**：
   ```bash
   # Windows CMD
   set PATH=D:\Qt\6.9.1\mingw_64\bin;D:\Qt\Tools\mingw1120_64\bin;%PATH%
   
   # Bash
   export PATH="/d/Qt/6.9.1/mingw_64/bin:/d/Qt/Tools/mingw1120_64/bin:$PATH"
   ```

2. **生成Makefile**：
   ```bash
   qmake DataDrivenRenamer.pro
   ```

3. **编译项目**：
   ```bash
   mingw32-make
   ```

### 方案3：使用Qt Creator（最简单）

1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择 `DataDrivenRenamer.pro` 文件
4. Qt Creator会自动配置正确的编译器和Qt版本
5. 点击构建按钮

## 构建结果

成功构建后，可执行文件位于：
- `release/DataDrivenRenamer.exe`

## 运行应用程序

确保Qt DLL在PATH中，然后运行：
```bash
./release/DataDrivenRenamer.exe
```

## VSCode 使用说明

现在您可以在VSCode中：

1. **查看代码**：所有Qt头文件现在都能正确识别，不会再显示红色错误
2. **构建项目**：
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 "Tasks: Run Task"
   - 选择 "Build with mingw32-make" 或 "Full Build"
3. **调试程序**：
   - 按 `F5` 开始调试
   - 或者按 `Ctrl+Shift+P` 然后选择 "Debug: Start Debugging"

## 注意事项

- ✅ 环境变量已正确配置
- ✅ VSCode C++扩展配置已完成
- ✅ 项目可以正常构建和运行
- 建议使用qmake构建系统，因为它与Qt集成更好
- 如果修改了Qt安装路径，需要更新`.vscode/c_cpp_properties.json`中的路径
