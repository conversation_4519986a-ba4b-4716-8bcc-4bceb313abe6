# DataDrivenRenamer 构建说明

## 问题解决

您遇到的"无法打开源文件"错误是由于Qt环境配置问题导致的。具体原因：

1. **编译器不匹配**：您的系统安装了Qt 6.9.1的MinGW版本，但IDE尝试使用MSVC编译器
2. **环境变量未设置**：Qt的bin目录没有添加到PATH环境变量中
3. **C++17支持问题**：MSVC编译器需要特殊的编译选项来支持Qt6

## 解决方案

### 方案1：使用提供的构建脚本（推荐）

#### Windows批处理脚本
```bash
build_with_qt.bat
```

#### Bash脚本（Git Bash/WSL）
```bash
./build_with_qt.sh
```

### 方案2：手动构建

1. **设置环境变量**：
   ```bash
   # Windows CMD
   set PATH=D:\Qt\6.9.1\mingw_64\bin;D:\Qt\Tools\mingw1120_64\bin;%PATH%
   
   # Bash
   export PATH="/d/Qt/6.9.1/mingw_64/bin:/d/Qt/Tools/mingw1120_64/bin:$PATH"
   ```

2. **生成Makefile**：
   ```bash
   qmake DataDrivenRenamer.pro
   ```

3. **编译项目**：
   ```bash
   mingw32-make
   ```

### 方案3：使用Qt Creator（最简单）

1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择 `DataDrivenRenamer.pro` 文件
4. Qt Creator会自动配置正确的编译器和Qt版本
5. 点击构建按钮

## 构建结果

成功构建后，可执行文件位于：
- `release/DataDrivenRenamer.exe`

## 运行应用程序

确保Qt DLL在PATH中，然后运行：
```bash
./release/DataDrivenRenamer.exe
```

## 注意事项

- 确保使用与Qt版本匹配的编译器（MinGW for MinGW Qt，MSVC for MSVC Qt）
- 如果使用CMake，需要添加MSVC特定的编译选项
- 建议使用qmake构建系统，因为它与Qt集成更好
