# Build directories
build/
build_test/
release/
debug/

# Qt generated files
Makefile*
*.pro.user
*.pro.user.*
.qmake.stash
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qm

# Object files
*.o
*.obj

# Executables
*.exe
*.app

# Resource files (generated)
*_resource.rc
*_resource_res.o

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
*.vcxproj*
*.sln

# IDE files
.vs/
*.user
*.suo
*.ncb
*.aps
*.plg
*.opt
*.clw
*.pdb
*.ilk

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
