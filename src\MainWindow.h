#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSplitter>
#include <QMenuBar>
#include <QStatusBar>
#include <QProgressBar>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>

class FileListWidget;
class ClipboardDataWidget;
class PreviewWidget;
class RenameEngine;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onExecuteRename();
    void onClearAll();
    void onAbout();
    void updatePreview();
    void onRenameProgress(int current, int total);
    void onRenameCompleted(int successful, int failed);

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void connectSignals();
    void setupSourceFilesPanel();
    void setupNewNamesPanel();
    void setupPreviewPanel();
    
    // UI Components
    QWidget *m_centralWidget;
    QHBoxLayout *m_mainLayout;
    QSplitter *m_splitter;
    
    // Three main panels
    QGroupBox *m_sourceFilesGroup;
    QGroupBox *m_newNamesGroup;
    QGroupBox *m_previewGroup;
    
    FileListWidget *m_fileListWidget;
    ClipboardDataWidget *m_clipboardWidget;
    PreviewWidget *m_previewWidget;
    
    // Control buttons
    QPushButton *m_executeButton;
    QPushButton *m_clearButton;
    
    // Status bar components
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    
    // Core engine
    RenameEngine *m_renameEngine;
};

#endif // MAINWINDOW_H
