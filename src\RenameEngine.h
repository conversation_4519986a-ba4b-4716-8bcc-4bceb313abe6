#ifndef RENAMEENGINE_H
#define RENAMEENGINE_H

#include <QObject>
#include <QStringList>
#include <QFileInfo>
#include <QDir>
#include <QTimer>
#include "PreviewWidget.h"

class RenameEngine : public QObject
{
    Q_OBJECT

public:
    explicit RenameEngine(QObject *parent = nullptr);
    
    // Main rename operation
    void executeRename(const QList<PreviewWidget::RenameMapping> &mappings);
    
    // Validation
    bool validateMappings(const QList<PreviewWidget::RenameMapping> &mappings, QStringList &errors);
    
    // Conflict detection
    bool hasConflicts(const QList<PreviewWidget::RenameMapping> &mappings, QStringList &conflicts);

signals:
    void renameProgress(int current, int total);
    void renameCompleted(int successful, int failed);
    void renameError(const QString &error);

private slots:
    void processNextRename();

private:
    struct RenameOperation {
        QString sourcePath;
        QString targetPath;
        QString backupPath;
        bool completed;
        bool success;
        QString errorMessage;
    };
    
    void prepareRenameOperations(const QList<PreviewWidget::RenameMapping> &mappings);
    bool performSingleRename(RenameOperation &operation);
    void rollbackOperations();
    QString generateBackupPath(const QString &originalPath);
    bool isValidFileName(const QString &fileName);
    
    // Data
    QList<RenameOperation> m_operations;
    int m_currentOperation;
    QTimer *m_timer;
    bool m_rollbackOnError;
};

#endif // RENAMEENGINE_H
