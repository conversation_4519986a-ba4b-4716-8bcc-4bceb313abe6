# Generated by CMake. Changes will be overwritten.
D:/Desktop/Project_Dev/DataDrivenRenamer/src/ClipboardDataWidget.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/ClipboardDataWidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/QClipboard
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QListWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QSplitter
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/MappingAlgorithm.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/FileListWidget.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/FileListWidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QMimeData
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QRect
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QSize
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/QDragEnterEvent
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/QDropEvent
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/QTransform
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QListWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/RenameEngine.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/MainWindow.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/MainWindow.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMenuBar
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QSplitter
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/FileListWidget.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/MappingAlgorithm.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/MappingAlgorithm.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/PreviewWidget.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/PreviewWidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHeaderView
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTreeWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTreeWidgetItem
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/RenameEngine.h
 mmc:Q_OBJECT
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/PreviewWidget.h
 mdp:D:/Desktop/Project_Dev/DataDrivenRenamer/src/RenameEngine.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QDir
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QFileInfo
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/QTimer
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHeaderView
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTreeWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTreeWidgetItem
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
D:/Desktop/Project_Dev/DataDrivenRenamer/src/ClipboardDataWidget.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/MainWindow.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/PreviewWidget.cpp
D:/Desktop/Project_Dev/DataDrivenRenamer/src/main.cpp
