#ifndef MAPPINGALGORITHM_H
#define MAPPINGALGORITHM_H

#include <QStringList>
#include <QObject>

class MappingAlgorithm : public QObject
{
    Q_OBJECT

public:
    explicit MappingAlgorithm(QObject *parent = nullptr);
    
    enum MappingMode {
        OneToOne,           // 一对一映射（默认）
        TruncateSource,     // 截断源列表以匹配目标列表
        TruncateTarget,     // 截断目标列表以匹配源列表
        PadTarget,          // 用默认名称填充目标列表
        PadSource           // 跳过多余的目标名称
    };
    
    struct MappingResult {
        QStringList sourceFiles;
        QStringList targetNames;
        QStringList warnings;
        bool isValid;
        int mappedCount;
        int skippedSource;
        int skippedTarget;
    };
    
    // Main mapping function
    static MappingResult createMapping(
        const QStringList &sourceFiles, 
        const QStringList &targetNames,
        MappingMode mode = OneToOne
    );
    
    // Utility functions
    static bool validateLists(const QStringList &sourceFiles, const QStringList &targetNames, QStringList &errors);
    static QStringList sanitizeFileNames(const QStringList &names);
    static QString sanitizeFileName(const QString &name);
    static bool isValidFileName(const QString &name);
    
    // Analysis functions
    static int findOptimalMapping(const QStringList &sourceFiles, const QStringList &targetNames);
    static QStringList suggestMappingMode(const QStringList &sourceFiles, const QStringList &targetNames);

private:
    static QString generateDefaultName(const QString &originalName, int index);
    static QStringList removeDuplicates(const QStringList &names);
};

#endif // MAPPINGALGORITHM_H
