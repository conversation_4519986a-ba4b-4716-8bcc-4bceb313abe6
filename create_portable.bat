@echo off
echo ========================================
echo 创建 DataDrivenRenamer 便携版
echo ========================================

REM 运行部署脚本
call deploy.bat
if %errorlevel% neq 0 (
    echo 错误: 部署失败
    pause
    exit /b 1
)

REM 获取版本信息
for /f "tokens=*" %%i in ('powershell -command "Get-Date -Format 'yyyyMMdd'"') do set DATE=%%i

REM 创建便携版文件夹
set PORTABLE_NAME=DataDrivenRenamer_Portable_%DATE%
if exist "%PORTABLE_NAME%" rmdir /s /q "%PORTABLE_NAME%"
mkdir "%PORTABLE_NAME%"

REM 复制部署文件
echo 创建便携版文件夹...
xcopy "deploy\*" "%PORTABLE_NAME%\" /E /I /H /Y

REM 创建启动脚本
echo 创建启动脚本...
echo @echo off > "%PORTABLE_NAME%\启动程序.bat"
echo echo 正在启动 DataDrivenRenamer... >> "%PORTABLE_NAME%\启动程序.bat"
echo start "" "DataDrivenRenamer.exe" >> "%PORTABLE_NAME%\启动程序.bat"

REM 创建详细说明文件
echo 创建使用说明...
echo DataDrivenRenamer - 数据驱动批量重命名工具 > "%PORTABLE_NAME%\使用说明.txt"
echo ================================================ >> "%PORTABLE_NAME%\使用说明.txt"
echo. >> "%PORTABLE_NAME%\使用说明.txt"
echo 【软件介绍】 >> "%PORTABLE_NAME%\使用说明.txt"
echo 这是一个基于数据映射的批量文件重命名工具，支持从Excel、网页等来源 >> "%PORTABLE_NAME%\使用说明.txt"
echo 复制文件名列表，快速批量重命名文件。 >> "%PORTABLE_NAME%\使用说明.txt"
echo. >> "%PORTABLE_NAME%\使用说明.txt"
echo 【使用步骤】 >> "%PORTABLE_NAME%\使用说明.txt"
echo 1. 双击"DataDrivenRenamer.exe"或"启动程序.bat"启动软件 >> "%PORTABLE_NAME%\使用说明.txt"
echo 2. 在左侧"源文件"面板中添加要重命名的文件 >> "%PORTABLE_NAME%\使用说明.txt"
echo    - 点击"添加文件"按钮选择文件 >> "%PORTABLE_NAME%\使用说明.txt"
echo    - 或直接拖拽文件到列表中 >> "%PORTABLE_NAME%\使用说明.txt"
echo 3. 在右上方文本框中粘贴或输入新的文件名列表 >> "%PORTABLE_NAME%\使用说明.txt"
echo    - 支持从Excel复制粘贴 >> "%PORTABLE_NAME%\使用说明.txt"
echo    - 支持从网页表格复制粘贴 >> "%PORTABLE_NAME%\使用说明.txt"
echo    - 每行一个文件名 >> "%PORTABLE_NAME%\使用说明.txt"
echo 4. 点击"解析数据"按钮处理文件名列表 >> "%PORTABLE_NAME%\使用说明.txt"
echo 5. 在下方预览区域查看重命名结果 >> "%PORTABLE_NAME%\使用说明.txt"
echo 6. 确认无误后点击"执行重命名"完成操作 >> "%PORTABLE_NAME%\使用说明.txt"
echo. >> "%PORTABLE_NAME%\使用说明.txt"
echo 【重要特性】 >> "%PORTABLE_NAME%\使用说明.txt"
echo - 自动保留原文件扩展名（如.jpg、.pdf等） >> "%PORTABLE_NAME%\使用说明.txt"
echo - 支持拖拽操作，使用便捷 >> "%PORTABLE_NAME%\使用说明.txt"
echo - 重命名前会进行安全检查，避免文件冲突 >> "%PORTABLE_NAME%\使用说明.txt"
echo - 支持撤销操作，安全可靠 >> "%PORTABLE_NAME%\使用说明.txt"
echo. >> "%PORTABLE_NAME%\使用说明.txt"
echo 【系统要求】 >> "%PORTABLE_NAME%\使用说明.txt"
echo - Windows 7/8/10/11 >> "%PORTABLE_NAME%\使用说明.txt"
echo - 无需安装，解压即用 >> "%PORTABLE_NAME%\使用说明.txt"
echo. >> "%PORTABLE_NAME%\使用说明.txt"
echo 【版本信息】 >> "%PORTABLE_NAME%\使用说明.txt"
echo 版本: 1.0.0 >> "%PORTABLE_NAME%\使用说明.txt"
echo 构建日期: %DATE% >> "%PORTABLE_NAME%\使用说明.txt"

REM 创建压缩包（如果有7zip）
where 7z.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo 创建压缩包...
    7z.exe a -tzip "%PORTABLE_NAME%.zip" "%PORTABLE_NAME%\*"
    echo 压缩包已创建: %PORTABLE_NAME%.zip
) else (
    echo 注意: 未找到7zip，请手动压缩 %PORTABLE_NAME% 文件夹
)

echo ========================================
echo 便携版创建完成！
echo ========================================
echo 便携版文件夹: %PORTABLE_NAME%\
echo 主程序: %PORTABLE_NAME%\DataDrivenRenamer.exe
echo 使用说明: %PORTABLE_NAME%\使用说明.txt
echo.
echo 您可以将整个文件夹或压缩包分享给其他人
echo 其他人解压后直接运行即可，无需安装Qt环境
echo.
pause
