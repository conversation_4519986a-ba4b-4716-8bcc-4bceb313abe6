@echo off
echo Building DataDrivenRenamer with Qt6...

REM Set Qt environment
set PATH=D:\Qt\6.9.1\mingw_64\bin;D:\Qt\Tools\mingw1120_64\bin;%PATH%

REM Clean previous build
if exist release rmdir /s /q release
if exist debug rmdir /s /q debug
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release

REM Generate Makefile
echo Generating Makefile...
qmake DataDrivenRenamer.pro
if %errorlevel% neq 0 (
    echo Error: qmake failed
    pause
    exit /b 1
)

REM Build the project
echo Building project...
mingw32-make
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable: release\DataDrivenRenamer.exe
pause
