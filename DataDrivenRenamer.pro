QT += core widgets gui

CONFIG += c++17

TARGET = DataDrivenRenamer
TEMPLATE = app

# Define output directory
DESTDIR = .

# Source files
SOURCES += \
    src/main.cpp \
    src/MainWindow.cpp \
    src/FileListWidget.cpp \
    src/ClipboardDataWidget.cpp \
    src/PreviewWidget.cpp \
    src/RenameEngine.cpp \
    src/MappingAlgorithm.cpp

# Header files
HEADERS += \
    src/MainWindow.h \
    src/FileListWidget.h \
    src/ClipboardDataWidget.h \
    src/PreviewWidget.h \
    src/RenameEngine.h \
    src/MappingAlgorithm.h

# Include path
INCLUDEPATH += src

# Windows specific settings
win32 {
    VERSION = 1.0.0
    QMAKE_TARGET_COMPANY = "DataDrivenRenamer"
    QMAKE_TARGET_PRODUCT = "Data Driven Renamer"
    QMAKE_TARGET_DESCRIPTION = "A data mapping based batch file renamer"
    QMAKE_TARGET_COPYRIGHT = "Copyright 2024"
}

# Debug/Release configurations
CONFIG(debug, debug|release) {
    TARGET = $$join(TARGET,,,_debug)
    OBJECTS_DIR = debug
    MOC_DIR = debug
    RCC_DIR = debug
    UI_DIR = debug
}

CONFIG(release, debug|release) {
    OBJECTS_DIR = release
    MOC_DIR = release
    RCC_DIR = release
    UI_DIR = release
}
