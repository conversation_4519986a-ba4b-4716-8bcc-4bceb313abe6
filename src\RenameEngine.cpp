#include "RenameEngine.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>

RenameEngine::RenameEngine(QObject *parent)
    : QObject(parent)
    , m_currentOperation(0)
    , m_timer(new QTimer(this))
    , m_rollbackOnError(true)
{
    m_timer->setSingleShot(true);
    connect(m_timer, &QTimer::timeout, this, &RenameEngine::processNextRename);
}

void RenameEngine::executeRename(const QList<PreviewWidget::RenameMapping> &mappings)
{
    // Validate mappings first
    QStringList errors;
    if (!validateMappings(mappings, errors)) {
        emit renameError("验证失败: " + errors.join("; "));
        return;
    }
    
    // Check for conflicts
    QStringList conflicts;
    if (hasConflicts(mappings, conflicts)) {
        emit renameError("发现冲突: " + conflicts.join("; "));
        return;
    }
    
    // Prepare operations
    prepareRenameOperations(mappings);
    
    if (m_operations.isEmpty()) {
        emit renameCompleted(0, 0);
        return;
    }
    
    // Start processing
    m_currentOperation = 0;
    emit renameProgress(0, m_operations.count());
    
    // Use timer to avoid blocking UI
    m_timer->start(10);
}

bool RenameEngine::validateMappings(const QList<PreviewWidget::RenameMapping> &mappings, QStringList &errors)
{
    bool isValid = true;
    
    for (const auto &mapping : mappings) {
        if (!mapping.isValid) {
            errors.append(QString("无效映射: %1").arg(mapping.sourceFileName));
            isValid = false;
            continue;
        }
        
        // Check if source file exists
        if (!QFileInfo::exists(mapping.sourcePath)) {
            errors.append(QString("源文件不存在: %1").arg(mapping.sourcePath));
            isValid = false;
        }
        
        // Check if new filename is valid
        if (!isValidFileName(mapping.newFileName)) {
            errors.append(QString("无效的新文件名: %1").arg(mapping.newFileName));
            isValid = false;
        }
    }
    
    return isValid;
}

bool RenameEngine::hasConflicts(const QList<PreviewWidget::RenameMapping> &mappings, QStringList &conflicts)
{
    QStringList targetPaths;
    bool hasConflict = false;
    
    for (const auto &mapping : mappings) {
        if (!mapping.isValid) continue;
        
        // Check for duplicate target paths
        if (targetPaths.contains(mapping.newPath)) {
            conflicts.append(QString("重复的目标路径: %1").arg(mapping.newPath));
            hasConflict = true;
        } else {
            targetPaths.append(mapping.newPath);
        }
        
        // Check if target already exists (and is not the same as source)
        if (QFileInfo::exists(mapping.newPath) && mapping.newPath != mapping.sourcePath) {
            conflicts.append(QString("目标文件已存在: %1").arg(mapping.newPath));
            hasConflict = true;
        }
    }
    
    return hasConflict;
}

void RenameEngine::processNextRename()
{
    if (m_currentOperation >= m_operations.count()) {
        // All operations completed
        int successful = 0;
        int failed = 0;
        
        for (const auto &op : m_operations) {
            if (op.success) {
                successful++;
            } else {
                failed++;
            }
        }
        
        emit renameCompleted(successful, failed);
        return;
    }
    
    // Process current operation
    RenameOperation &operation = m_operations[m_currentOperation];
    bool success = performSingleRename(operation);
    
    if (!success && m_rollbackOnError) {
        // Rollback previous operations
        rollbackOperations();
        emit renameError(QString("重命名失败，已回滚: %1").arg(operation.errorMessage));
        return;
    }
    
    m_currentOperation++;
    emit renameProgress(m_currentOperation, m_operations.count());
    
    // Continue with next operation
    m_timer->start(10);
}

void RenameEngine::prepareRenameOperations(const QList<PreviewWidget::RenameMapping> &mappings)
{
    m_operations.clear();
    
    for (const auto &mapping : mappings) {
        if (!mapping.isValid) continue;
        
        RenameOperation operation;
        operation.sourcePath = mapping.sourcePath;
        operation.targetPath = mapping.newPath;
        operation.backupPath = generateBackupPath(mapping.sourcePath);
        operation.completed = false;
        operation.success = false;
        
        m_operations.append(operation);
    }
}

bool RenameEngine::performSingleRename(RenameOperation &operation)
{
    operation.completed = true;
    
    // If source and target are the same, no operation needed
    if (operation.sourcePath == operation.targetPath) {
        operation.success = true;
        return true;
    }
    
    // Create backup first (copy source to backup location)
    if (!QFile::copy(operation.sourcePath, operation.backupPath)) {
        operation.errorMessage = "无法创建备份文件";
        operation.success = false;
        return false;
    }
    
    // Perform rename
    QFile sourceFile(operation.sourcePath);
    if (!sourceFile.rename(operation.targetPath)) {
        operation.errorMessage = QString("重命名失败: %1").arg(sourceFile.errorString());
        operation.success = false;
        
        // Remove backup since rename failed
        QFile::remove(operation.backupPath);
        return false;
    }
    
    operation.success = true;
    return true;
}

void RenameEngine::rollbackOperations()
{
    // Rollback completed operations in reverse order
    for (int i = m_currentOperation - 1; i >= 0; --i) {
        const RenameOperation &operation = m_operations[i];
        
        if (operation.completed && operation.success) {
            // Restore from backup
            QFile::remove(operation.targetPath);
            QFile::rename(operation.backupPath, operation.sourcePath);
        }
        
        // Clean up backup
        QFile::remove(operation.backupPath);
    }
}

QString RenameEngine::generateBackupPath(const QString &originalPath)
{
    QFileInfo info(originalPath);
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss_zzz");
    QString backupName = QString("%1_backup_%2.%3")
                        .arg(info.baseName())
                        .arg(timestamp)
                        .arg(info.suffix());
    
    return info.absolutePath() + "/" + backupName;
}

bool RenameEngine::isValidFileName(const QString &fileName)
{
    if (fileName.isEmpty()) {
        return false;
    }
    
    // Check for invalid characters on Windows
    QRegularExpression invalidChars(R"([<>:"/\\|?*])");
    if (fileName.contains(invalidChars)) {
        return false;
    }
    
    // Check for reserved names on Windows
    QStringList reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
    
    QString baseName = QFileInfo(fileName).baseName().toUpper();
    return !reservedNames.contains(baseName);
}
