#!/bin/bash

echo "Building DataDrivenRenamer..."

# Check if qmake is available
if ! command -v qmake &> /dev/null; then
    echo "Error: qmake not found. Please install Qt development environment."
    echo "Ubuntu/Debian: sudo apt-get install qt6-base-dev qt6-tools-dev"
    echo "CentOS/RHEL: sudo yum install qt6-qtbase-devel qt6-qttools-devel"
    echo "macOS: brew install qt6"
    exit 1
fi

# Create build directory
mkdir -p build
cd build

# Generate Makefile with qmake
qmake ../DataDrivenRenamer.pro
if [ $? -ne 0 ]; then
    echo "qmake configuration failed!"
    exit 1
fi

# Build the project
make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo "Executable location: build/bin/DataDrivenRenamer"

# Make executable if needed
chmod +x bin/DataDrivenRenamer 2>/dev/null

echo "You can now run the application with: ./build/bin/DataDrivenRenamer"
