{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/src", "D:/Qt/6.9.1/mingw_64/include", "D:/Qt/6.9.1/mingw_64/include/QtCore", "D:/Qt/6.9.1/mingw_64/include/QtGui", "D:/Qt/6.9.1/mingw_64/include/QtWidgets", "D:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++", "D:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32", "D:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward", "D:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "MINGW_HAS_SECURE_API=1", "QT_WIDGETS_LIB", "QT_GUI_LIB", "QT_CORE_LIB", "QT_NEEDS_QMAIN"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "D:/Qt/Tools/mingw1310_64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "compilerArgs": ["-fno-keep-inline-dllexport", "-Wall", "-Wextra", "-fexceptions", "-mthreads"]}], "version": 4}