@echo off
echo Building DataDrivenRenamer...

REM Set Qt path for your installation
set QT_DIR=D:\Qt\6.9.1\mingw_64

REM Check if Qt directory exists
if not exist "%QT_DIR%" (
    echo Error: Qt directory not found at %QT_DIR%
    echo Please verify your Qt installation.
    pause
    exit /b 1
)

REM Set PATH to use ONLY Qt's MinGW (remove conflicting MinGW)
set PATH=%QT_DIR%\bin;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem

echo Using Qt from: %QT_DIR%
echo PATH set to use Qt's MinGW only

REM Create build directory
if not exist build mkdir build
cd build

REM Clean previous build
if exist Makefile (
    echo Cleaning previous build...
    mingw32-make clean
)

REM Generate Makefile with qmake
echo Running qmake...
qmake.exe ..\DataDrivenRenamer.pro
if %errorlevel% neq 0 (
    echo qmake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
mingw32-make
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable location: DataDrivenRenamer.exe
pause
