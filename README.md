# 数据驱动重命名器 (DataDrivenRenamer)

一个基于数据映射的批量文件重命名工具，使用C++和Qt6开发。

## 核心特性

### 🎯 数据映射重命名
- **不同于传统的规则模式重命名**：市面上绝大多数批量重命名软件都是基于"规则和模式"（比如添加前缀、替换文本）
- **基于数据映射**：建立两个有序列表之间的一一对应关系进行重命名

### 🖥️ 三栏布局界面
1. **源文件面板 (Source Files)**
   - 文件列表视图，显示待重命名的文件
   - 支持拖拽添加文件
   - 支持多选和批量操作

2. **新文件名来源面板 (New Filenames Source)**
   - 从剪贴板粘贴：支持从Excel、网页等复制内容直接粘贴
   - 智能解析多种数据格式
   - 实时预览解析结果

3. **预览与执行面板 (Preview & Execute)**
   - 实时显示原文件名→新文件名的映射关系
   - 冲突检测和错误提示
   - 安全的重命名执行

### 📋 强大的剪贴板支持
- 支持从Excel表格复制粘贴
- 支持从网页内容复制粘贴
- 智能识别制表符分隔、逗号分隔等格式
- 自动过滤编号、符号等无关内容

## 技术架构

- **开发语言**: C++17
- **GUI框架**: Qt6
- **构建系统**: CMake
- **支持平台**: Windows (可扩展到Linux/macOS)

## 项目结构

```
DataDrivenRenamer/
├── src/
│   ├── main.cpp                 # 程序入口
│   ├── MainWindow.h/cpp         # 主窗口
│   ├── FileListWidget.h/cpp     # 源文件管理组件
│   ├── ClipboardDataWidget.h/cpp # 剪贴板数据处理组件
│   ├── PreviewWidget.h/cpp      # 预览组件
│   ├── RenameEngine.h/cpp       # 重命名执行引擎
│   └── MappingAlgorithm.h/cpp   # 映射算法
├── CMakeLists.txt               # CMake配置
├── build.bat                    # Windows构建脚本
└── README.md                    # 项目说明
```

## 构建说明

### 前置要求
- Qt6 开发环境
- CMake 3.16+
- C++17 兼容编译器 (GCC/MSVC/Clang)

### Windows构建
```bash
# 使用提供的构建脚本
build.bat

# 或手动构建
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

### 运行
```bash
# 构建完成后
cd build/bin
./DataDrivenRenamer.exe
```

## 使用方法

1. **添加源文件**
   - 点击"添加文件"按钮选择文件
   - 或直接拖拽文件到左侧面板

2. **准备新文件名**
   - 在Excel或网页中复制文件名列表
   - 点击"从剪贴板粘贴"按钮
   - 或直接在文本框中输入/粘贴数据

3. **预览映射关系**
   - 中间面板会自动显示映射预览
   - 检查是否有冲突或错误

4. **执行重命名**
   - 确认无误后点击"执行重命名"
   - 系统会安全地执行重命名操作

## 开发状态

当前项目处于开发阶段，已完成：
- [x] 项目结构和CMake配置
- [x] 主窗口三栏布局设计
- [x] 源文件管理功能
- [x] 剪贴板数据处理功能
- [x] 预览功能基础实现
- [ ] 映射算法完善
- [ ] 重命名执行引擎
- [ ] 错误处理和用户体验优化

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目采用MIT许可证。
