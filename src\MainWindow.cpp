#include "MainWindow.h"
#include "FileListWidget.h"
#include "ClipboardDataWidget.h"
#include "PreviewWidget.h"
#include "RenameEngine.h"
#include <QApplication>
#include <QMessageBox>
#include <QMenuBar>
#include <QStatusBar>
#include <QProgressBar>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSplitter>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_splitter(nullptr)
    , m_sourceFilesGroup(nullptr)
    , m_newNamesGroup(nullptr)
    , m_previewGroup(nullptr)
    , m_fileListWidget(nullptr)
    , m_clipboardWidget(nullptr)
    , m_previewWidget(nullptr)
    , m_executeButton(nullptr)
    , m_clearButton(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_renameEngine(nullptr)
{
    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    
    // Initialize rename engine
    m_renameEngine = new RenameEngine(this);

    // Connect rename engine signals
    connect(m_renameEngine, &RenameEngine::renameProgress, this, &MainWindow::onRenameProgress);
    connect(m_renameEngine, &RenameEngine::renameCompleted, this, &MainWindow::onRenameCompleted);
    connect(m_renameEngine, &RenameEngine::renameError, this, [this](const QString &error) {
        m_progressBar->setVisible(false);
        m_executeButton->setEnabled(true);
        m_clearButton->setEnabled(true);
        QMessageBox::critical(this, "重命名错误", error);
        m_statusLabel->setText("重命名失败");
    });
    
    setWindowTitle("数据驱动重命名器 - DataDrivenRenamer v1.0");
    setMinimumSize(1200, 700);
    resize(1400, 800);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_splitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_splitter);

    // Create three main panels
    setupSourceFilesPanel();
    setupNewNamesPanel();
    setupPreviewPanel();

    // Set splitter proportions
    m_splitter->setStretchFactor(0, 1);
    m_splitter->setStretchFactor(1, 1);
    m_splitter->setStretchFactor(2, 1);
}

void MainWindow::setupSourceFilesPanel()
{
    m_sourceFilesGroup = new QGroupBox("源文件 (Source Files)", this);
    QVBoxLayout *layout = new QVBoxLayout(m_sourceFilesGroup);

    m_fileListWidget = new FileListWidget(this);
    layout->addWidget(m_fileListWidget);

    m_splitter->addWidget(m_sourceFilesGroup);
}

void MainWindow::setupNewNamesPanel()
{
    m_newNamesGroup = new QGroupBox("新文件名来源 (New Filenames Source)", this);
    QVBoxLayout *layout = new QVBoxLayout(m_newNamesGroup);

    m_clipboardWidget = new ClipboardDataWidget(this);
    layout->addWidget(m_clipboardWidget);

    m_splitter->addWidget(m_newNamesGroup);
}

void MainWindow::setupPreviewPanel()
{
    m_previewGroup = new QGroupBox("预览与执行 (Preview & Execute)", this);
    QVBoxLayout *layout = new QVBoxLayout(m_previewGroup);

    m_previewWidget = new PreviewWidget(this);
    layout->addWidget(m_previewWidget);

    // Control buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_executeButton = new QPushButton("执行重命名", this);
    m_clearButton = new QPushButton("清空所有", this);

    m_executeButton->setEnabled(false);
    m_executeButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }");
    m_clearButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }");

    buttonLayout->addWidget(m_executeButton);
    buttonLayout->addWidget(m_clearButton);
    layout->addLayout(buttonLayout);

    m_splitter->addWidget(m_previewGroup);
}

void MainWindow::setupMenuBar()
{
    QMenu *fileMenu = menuBar()->addMenu("文件(&F)");
    fileMenu->addAction("退出(&X)", this, &QWidget::close, QKeySequence::Quit);
    
    QMenu *helpMenu = menuBar()->addMenu("帮助(&H)");
    helpMenu->addAction("关于(&A)", this, &MainWindow::onAbout);
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("就绪", this);
    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    
    statusBar()->addWidget(m_statusLabel, 1);
    statusBar()->addPermanentWidget(m_progressBar);
}

void MainWindow::connectSignals()
{
    connect(m_executeButton, &QPushButton::clicked, this, &MainWindow::onExecuteRename);
    connect(m_clearButton, &QPushButton::clicked, this, &MainWindow::onClearAll);

    // Connect widgets to update preview
    connect(m_fileListWidget, &FileListWidget::filesChanged, this, &MainWindow::updatePreview);
    connect(m_clipboardWidget, &ClipboardDataWidget::dataChanged, this, &MainWindow::updatePreview);

    // Connect rename engine signals (will be connected when engine is created)
}

void MainWindow::updatePreview()
{
    bool hasFiles = m_fileListWidget->getFileCount() > 0;
    bool hasNames = m_clipboardWidget->getNameCount() > 0;

    if (hasFiles && hasNames) {
        // Update preview widget
        QStringList sourceFiles = m_fileListWidget->getFilePaths();
        QStringList newNames = m_clipboardWidget->getNewNames();
        m_previewWidget->updatePreview(sourceFiles, newNames);

        // Enable execute button only if there are valid mappings
        int validMappings = 0;
        for (const auto &mapping : m_previewWidget->getMappings()) {
            if (mapping.isValid) {
                validMappings++;
            }
        }

        m_executeButton->setEnabled(validMappings > 0);

        m_statusLabel->setText(QString("准备就绪 - %1 个文件，%2 个新名称，%3 个有效映射")
                              .arg(m_fileListWidget->getFileCount())
                              .arg(m_clipboardWidget->getNameCount())
                              .arg(validMappings));
    } else {
        m_previewWidget->clear();
        m_executeButton->setEnabled(false);

        if (hasFiles) {
            m_statusLabel->setText(QString("已选择 %1 个文件，等待新文件名数据").arg(m_fileListWidget->getFileCount()));
        } else if (hasNames) {
            m_statusLabel->setText(QString("已准备 %1 个新文件名，等待选择源文件").arg(m_clipboardWidget->getNameCount()));
        } else {
            m_statusLabel->setText("就绪");
        }
    }
}

void MainWindow::onExecuteRename()
{
    if (!m_renameEngine) {
        QMessageBox::critical(this, "错误", "重命名引擎未初始化");
        return;
    }

    // Get mappings from preview widget
    QList<PreviewWidget::RenameMapping> mappings = m_previewWidget->getMappings();

    if (mappings.isEmpty()) {
        QMessageBox::information(this, "提示", "没有可执行的重命名操作");
        return;
    }

    // Count valid mappings
    int validCount = 0;
    for (const auto &mapping : mappings) {
        if (mapping.isValid) {
            validCount++;
        }
    }

    if (validCount == 0) {
        QMessageBox::warning(this, "警告", "没有有效的重命名映射");
        return;
    }

    // Confirm with user
    int ret = QMessageBox::question(this, "确认重命名",
        QString("即将重命名 %1 个文件，此操作不可撤销。\n\n确定要继续吗？").arg(validCount),
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret != QMessageBox::Yes) {
        return;
    }

    // Disable UI during rename operation
    m_executeButton->setEnabled(false);
    m_clearButton->setEnabled(false);

    // Execute rename
    m_renameEngine->executeRename(mappings);
}

void MainWindow::onClearAll()
{
    m_fileListWidget->clear();
    m_clipboardWidget->clear();
    m_previewWidget->clear();
    updatePreview();
}

void MainWindow::onAbout()
{
    QMessageBox::about(this, "关于",
        "<h3>数据驱动重命名器 v1.0</h3>"
        "<p>一个基于数据映射的批量文件重命名工具</p>"
        "<p>核心特性：</p>"
        "<ul>"
        "<li>建立两个有序列表之间的一一对应关系</li>"
        "<li>支持从剪贴板粘贴Excel/网页数据</li>"
        "<li>实时预览重命名结果</li>"
        "<li>安全的重命名操作</li>"
        "</ul>"
        "<p>开发语言：C++ with Qt6</p>");
}

void MainWindow::onRenameProgress(int current, int total)
{
    m_progressBar->setVisible(true);
    m_progressBar->setMaximum(total);
    m_progressBar->setValue(current);
    m_statusLabel->setText(QString("正在重命名... (%1/%2)").arg(current).arg(total));
}

void MainWindow::onRenameCompleted(int successful, int failed)
{
    m_progressBar->setVisible(false);
    m_executeButton->setEnabled(true);
    m_clearButton->setEnabled(true);

    m_statusLabel->setText(QString("重命名完成 - 成功: %1, 失败: %2").arg(successful).arg(failed));

    if (failed > 0) {
        QMessageBox::warning(this, "重命名完成",
            QString("重命名操作完成\n成功: %1\n失败: %2").arg(successful).arg(failed));
    } else {
        QMessageBox::information(this, "重命名完成",
            QString("所有文件重命名成功！\n共处理 %1 个文件").arg(successful));
    }

    // Refresh the file list to show new names
    if (successful > 0) {
        // Clear and refresh to show updated file names
        updatePreview();
    }
}
